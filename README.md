# Gemini Chat - AI Conversational Assistant

A modern, responsive chat application built with Next.js 15 that simulates conversations with Gemini AI. Features include OTP-based authentication, real-time messaging simulation, image sharing, and a polished user experience.

## 🌟 Features

### Authentication
- **OTP-based Login/Signup** - Secure phone number verification
- **Country Code Selection** - Fetches real country data from REST Countries API
- **Form Validation** - React Hook Form + Zod for robust validation
- **Simulated OTP** - Realistic OTP generation and verification flow

### Chat Experience
- **Multiple Chatrooms** - Create, manage, and delete chat conversations
- **AI Message Simulation** - Intelligent responses with realistic delays and throttling
- **Image Upload** - Share images with base64 conversion and preview
- **Typing Indicators** - "<PERSON> is typing..." with animated dots
- **Message Features** - Copy to clipboard, timestamps, message history

### Advanced UX
- **Infinite Scroll** - Reverse pagination for message history
- **Search Functionality** - Debounced search across chatrooms
- **Dark Mode** - Toggle between light and dark themes
- **Toast Notifications** - Success, error, and info messages
- **Loading Skeletons** - Smooth loading states
- **Mobile Responsive** - Optimized for all device sizes
- **Keyboard Accessibility** - Full keyboard navigation support

## 🛠 Tech Stack

- **Framework**: Next.js 15 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS v4
- **State Management**: Zustand with persistence
- **Form Handling**: React Hook Form
- **Validation**: Zod
- **Icons**: Lucide React
- **Notifications**: React Hot Toast

## 📁 Project Structure

```
kukava/
├── app/                    # Next.js app directory
│   ├── globals.css        # Global styles and animations
│   ├── layout.tsx         # Root layout with metadata
│   └── page.tsx           # Main page component
├── components/            # React components
│   ├── auth/             # Authentication components
│   │   ├── AuthPage.tsx
│   │   ├── CountrySelector.tsx
│   │   ├── LoginForm.tsx
│   │   └── OTPForm.tsx
│   ├── chat/             # Chat interface components
│   │   ├── ChatInput.tsx
│   │   ├── ChatInterface.tsx
│   │   ├── ChatMessage.tsx
│   │   └── TypingIndicator.tsx
│   ├── dashboard/        # Dashboard components
│   │   ├── ChatroomList.tsx
│   │   ├── CreateChatroomModal.tsx
│   │   └── Dashboard.tsx
│   ├── ui/               # Reusable UI components
│   │   ├── LoadingSkeleton.tsx
│   │   └── Toast.tsx
│   └── App.tsx           # Main app component
├── stores/               # Zustand stores
│   ├── authStore.ts      # Authentication state
│   ├── chatStore.ts      # Chat and messaging state
│   └── uiStore.ts        # UI state (theme, toasts)
├── types/                # TypeScript type definitions
│   └── index.ts
├── lib/                  # Utility functions
│   ├── countryService.ts # Country data fetching
│   ├── utils.ts          # General utilities
│   └── validations.ts    # Zod schemas
└── hooks/                # Custom React hooks (future)
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd kukava
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

### Build for Production

```bash
npm run build
npm start
```

## 📱 Usage Guide

### Getting Started
1. **Phone Verification**: Enter your phone number with country code
2. **OTP Verification**: Enter the 6-digit code (check console for simulated OTP)
3. **Create Chat**: Click "New Chat" to start a conversation
4. **Send Messages**: Type messages or upload images to chat with Gemini

### Key Features
- **Search**: Use Ctrl/Cmd + / to focus search
- **New Chat**: Use Ctrl/Cmd + N to create new chat
- **Theme Toggle**: Click sun/moon icon to switch themes
- **Copy Messages**: Hover over messages to copy content
- **Image Upload**: Click image icon in chat input

## 🔧 Implementation Details

### Authentication Flow
- Country selection with real API data
- Simulated OTP generation (6-digit codes)
- Form validation with error handling
- Persistent login state

### AI Response Simulation
- **Throttling**: Minimum 1-second delay between responses
- **Context Awareness**: Different responses for greetings, questions, images
- **Realistic Delays**: Based on message complexity and length
- **Typing Indicators**: Visual feedback during AI "thinking"

### Message Pagination
- **Reverse Infinite Scroll**: Load older messages when scrolling to top
- **Client-side Pagination**: 20 messages per page
- **Dummy Data Generation**: Simulated message history
- **Scroll Position Maintenance**: Preserves position when loading more

### Form Validation
- **Phone Numbers**: 6-15 digits, country-specific validation
- **OTP Codes**: Exactly 6 digits
- **Images**: Type and size validation (5MB limit)
- **Real-time Validation**: Immediate feedback on input

### Responsive Design
- **Mobile-first**: Optimized for touch interfaces
- **Breakpoint System**: Tailored layouts for different screen sizes
- **Touch Gestures**: Swipe-friendly navigation
- **Accessibility**: WCAG 2.1 compliant

## 🎨 Customization

### Themes
The app supports light and dark modes with custom color schemes defined in `globals.css`. Colors are defined using OKLCH color space for better perceptual uniformity.

### Animations
Custom animations are defined for:
- Page transitions
- Toast notifications
- Loading states
- Hover effects

### Configuration
Key configuration options in:
- `stores/chatStore.ts` - Message pagination, AI response patterns
- `lib/countryService.ts` - Country data and popular countries
- `lib/validations.ts` - Form validation rules

## 🚀 Deployment

### Vercel (Recommended)
1. Push code to GitHub
2. Connect repository to Vercel
3. Deploy automatically

### Other Platforms
The app is a standard Next.js application and can be deployed to:
- Netlify
- AWS Amplify
- Railway
- DigitalOcean App Platform

### Environment Variables
No environment variables required for basic functionality. All features work with simulated data.

## 🧪 Testing

### Manual Testing Checklist
- [ ] Phone number validation with different country codes
- [ ] OTP verification flow
- [ ] Chat creation and deletion
- [ ] Message sending (text and images)
- [ ] Search functionality
- [ ] Theme switching
- [ ] Mobile responsiveness
- [ ] Keyboard navigation
- [ ] Toast notifications

### Browser Compatibility
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) - React framework
- [Tailwind CSS](https://tailwindcss.com/) - Utility-first CSS
- [Zustand](https://github.com/pmndrs/zustand) - State management
- [REST Countries](https://restcountries.com/) - Country data API
- [Lucide](https://lucide.dev/) - Icon library
